'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { StudentInfo, User, BatchGenerationRequest, BatchGenerationResponse } from '@/types';
import CustomSelectField from '@/components/CustomSelectField';
import * as XLSX from 'xlsx';

export default function BatchGenerationPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [students, setStudents] = useState<StudentInfo[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 全局设置
  const [globalSettings, setGlobalSettings] = useState({
    commentTone: '温和亲切',
    wordCountRange: '200-300字',
    commentPerspective: '你' as '你' | '该生'
  });

  // 检查用户登录状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/login');
    }
  }, [router]);

  // 创建默认学生信息
  const createDefaultStudent = (): StudentInfo => ({
    studentName: '',
    studentGender: '男',
    subjectStrengths: '',
    subjectWeaknesses: '',
    learningPotential: '学习能力稳定',
    subjectInterest: '一般',
    classroomConcentration: '大部分时间专注',
    homeworkCompletion: '大部分按时完成',
    learningProactiveness: '完成要求任务',
    disciplineCompliance: '基本遵守',
    attitudeTowardsOthers: '基本有礼貌',
    responsibility: '能完成分配任务',
    talentsAndInterests: '',
    classPosition: '无职位',
    awards: '',
    overallAssessment: '',
    futureExpectations: '',
    improvementSuggestions: '',
    commentPerspective: globalSettings.commentPerspective,
    commentTone: globalSettings.commentTone as '温和亲切' | '严谨正式' | '鼓励激励' | '客观中性',
    wordCountRange: globalSettings.wordCountRange as '150-200字' | '200-300字' | '300-400字' | '400-500字'
  });

  // 添加学生
  const addStudent = () => {
    setStudents([...students, createDefaultStudent()]);
    setShowDetailedFields([...showDetailedFields, false]);
  };

  // 批量添加学生（通过姓名列表）
  const [showBatchAdd, setShowBatchAdd] = useState(false);
  const [batchNames, setBatchNames] = useState('');

  // 控制每个学生是否显示详细字段
  const [showDetailedFields, setShowDetailedFields] = useState<boolean[]>([]);

  const addStudentsBatch = () => {
    const names = batchNames.split('\n').filter(name => name.trim());
    const newStudents = names.map(name => ({
      ...createDefaultStudent(),
      studentName: name.trim()
    }));
    const newDetailedFieldsStates = new Array(names.length).fill(false);
    setStudents([...students, ...newStudents]);
    setShowDetailedFields([...showDetailedFields, ...newDetailedFieldsStates]);
    setBatchNames('');
    setShowBatchAdd(false);
  };

  // Excel导入功能
  const handleExcelImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        const importedStudents = (jsonData as Record<string, unknown>[]).map((row) => ({
          ...createDefaultStudent(),
          studentName: String(row['学生姓名'] || row['姓名'] || ''),
          studentGender: (row['性别'] === '女' ? '女' : '男') as '男' | '女',
          classPosition: String(row['班干职位'] || '无职位'),
          talentsAndInterests: String(row['特长与兴趣'] || ''),
          awards: String(row['获奖情况'] || '')
        }));

        const newDetailedFieldsStates = new Array(importedStudents.length).fill(false);
        setStudents([...students, ...importedStudents]);
        setShowDetailedFields([...showDetailedFields, ...newDetailedFieldsStates]);
        alert(`成功导入 ${importedStudents.length} 个学生`);
      } catch (error) {
        console.error('Excel导入失败:', error);
        alert('Excel导入失败，请检查文件格式');
      }
    };
    reader.readAsArrayBuffer(file);

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 下载Excel模板
  const downloadTemplate = () => {
    const templateData = [
      {
        '学生姓名': '张三',
        '性别': '男',
        '班干职位': '班长',
        '特长与兴趣': '爱好绘画，作品有创意',
        '获奖情况': '校三好学生、数学竞赛二等奖'
      },
      {
        '学生姓名': '李四',
        '性别': '女',
        '班干职位': '学习委员',
        '特长与兴趣': '篮球特长，是校队成员',
        '获奖情况': '优秀班干部、英语演讲比赛一等奖'
      }
    ];

    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '学生信息');
    XLSX.writeFile(workbook, '学生信息导入模板.xlsx');
  };

  // 删除学生
  const removeStudent = (index: number) => {
    setStudents(students.filter((_, i) => i !== index));
    setShowDetailedFields(showDetailedFields.filter((_, i) => i !== index));
  };

  // 更新学生信息
  const updateStudent = (index: number, field: keyof StudentInfo, value: string) => {
    const updatedStudents = [...students];
    updatedStudents[index] = {
      ...updatedStudents[index],
      [field]: value
    };
    setStudents(updatedStudents);
  };

  // 应用全局设置到所有学生
  const applyGlobalSettings = () => {
    const updatedStudents = students.map(student => ({
      ...student,
      commentTone: globalSettings.commentTone as '温和亲切' | '严谨正式' | '鼓励激励' | '客观中性',
      wordCountRange: globalSettings.wordCountRange as '150-200字' | '200-300字' | '300-400字' | '400-500字',
      commentPerspective: globalSettings.commentPerspective
    }));
    setStudents(updatedStudents);
  };

  // 开始批量生成
  const startBatchGeneration = async () => {
    if (students.length === 0) {
      alert('请至少添加一个学生');
      return;
    }

    // 验证必填字段
    for (let i = 0; i < students.length; i++) {
      if (!students[i].studentName.trim()) {
        alert(`第${i + 1}个学生的姓名不能为空`);
        return;
      }
    }

    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const requestData: BatchGenerationRequest = {
        students,
        globalSettings
      };

      const response = await fetch('/api/batch-generate-comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      const data: BatchGenerationResponse = await response.json();

      if (data.success && data.batchTaskId) {
        // 跳转到批量生成状态页面
        router.push(`/batch/status/${data.batchTaskId}`);
      } else {
        alert(data.message || '启动批量生成失败');
      }
    } catch (error) {
      console.error('批量生成错误:', error);
      alert('批量生成失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 退出登录
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">批量评语生成</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">欢迎，{user.username}</span>
              <button
                onClick={() => router.push('/')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                单个生成
              </button>
              <button
                onClick={() => router.push('/history')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                历史评语
              </button>
              {user.is_admin && (
                <button
                  onClick={() => router.push('/admin')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  管理后台
                </button>
              )}
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 全局设置区域 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">全局设置</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                评语语气
              </label>
              <select
                value={globalSettings.commentTone}
                onChange={(e) => setGlobalSettings({...globalSettings, commentTone: e.target.value as '温和亲切' | '严谨正式' | '鼓励激励' | '客观中性'})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="温和亲切">温和亲切</option>
                <option value="严谨正式">严谨正式</option>
                <option value="鼓励激励">鼓励激励</option>
                <option value="客观中性">客观中性</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                评语字数范围
              </label>
              <select
                value={globalSettings.wordCountRange}
                onChange={(e) => setGlobalSettings({...globalSettings, wordCountRange: e.target.value as '150-200字' | '200-300字' | '300-400字' | '400-500字'})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="150-200字">150-200字</option>
                <option value="200-300字">200-300字</option>
                <option value="300-400字">300-400字</option>
                <option value="400-500字">400-500字</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                评语人称
              </label>
              <select
                value={globalSettings.commentPerspective}
                onChange={(e) => setGlobalSettings({...globalSettings, commentPerspective: e.target.value as '你' | '该生'})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="你">你</option>
                <option value="该生">该生</option>
              </select>
            </div>
          </div>

          <div className="mt-4 flex space-x-2">
            <button
              onClick={applyGlobalSettings}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              应用到所有学生
            </button>
            <button
              onClick={() => {
                const allExpanded = showDetailedFields.every(show => show);
                setShowDetailedFields(new Array(students.length).fill(!allExpanded));
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              {showDetailedFields.every(show => show) ? '隐藏所有详细字段' : '显示所有详细字段'}
            </button>
          </div>
        </div>

        {/* 操作工具栏 */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={addStudent}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                添加学生
              </button>
              <button
                onClick={() => setShowBatchAdd(true)}
                className="px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
              >
                批量添加
              </button>
              <label className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 cursor-pointer">
                Excel导入
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleExcelImport}
                  className="hidden"
                />
              </label>
              <button
                onClick={downloadTemplate}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                下载模板
              </button>
              <span className="text-sm text-gray-600 flex items-center">
                已添加 {students.length} 个学生
              </span>
            </div>

            <button
              onClick={startBatchGeneration}
              disabled={loading || students.length === 0}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '启动中...' : '开始批量生成'}
            </button>
          </div>
        </div>

        {/* 批量添加学生对话框 */}
        {showBatchAdd && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">批量添加学生</h3>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  学生姓名（每行一个）
                </label>
                <textarea
                  value={batchNames}
                  onChange={(e) => setBatchNames(e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="张三&#10;李四&#10;王五&#10;..."
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={addStudentsBatch}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  添加
                </button>
                <button
                  onClick={() => {
                    setShowBatchAdd(false);
                    setBatchNames('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 学生列表 */}
        <div className="space-y-4">
          {students.map((student, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">学生 {index + 1}</h3>
                <button
                  onClick={() => removeStudent(index)}
                  className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
                >
                  删除
                </button>
              </div>

              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生姓名 *
                  </label>
                  <input
                    type="text"
                    value={student.studentName}
                    onChange={(e) => updateStudent(index, 'studentName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入学生姓名"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生性别
                  </label>
                  <select
                    value={student.studentGender}
                    onChange={(e) => updateStudent(index, 'studentGender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="男">男</option>
                    <option value="女">女</option>
                  </select>
                </div>

                <CustomSelectField
                  label="班干职位"
                  fieldName="classPosition"
                  value={student.classPosition}
                  onChange={(value) => updateStudent(index, 'classPosition', value)}
                  placeholder="请选择班干职位"
                />
              </div>

              {/* 基本字段 */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    特长与兴趣
                  </label>
                  <textarea
                    value={student.talentsAndInterests}
                    onChange={(e) => updateStudent(index, 'talentsAndInterests', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="例如：爱好绘画,作品有创意; 篮球特长,是校队成员"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    获奖情况
                  </label>
                  <textarea
                    value={student.awards}
                    onChange={(e) => updateStudent(index, 'awards', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="例如：校三好学生、数学竞赛二等奖、优秀班干部等"
                  />
                </div>
              </div>

              {/* 详细字段切换按钮 */}
              <div className="mt-4 flex justify-center">
                <button
                  type="button"
                  onClick={() => {
                    const newShowDetailedFields = [...showDetailedFields];
                    newShowDetailedFields[index] = !newShowDetailedFields[index];
                    setShowDetailedFields(newShowDetailedFields);
                  }}
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                >
                  {showDetailedFields[index] ? '隐藏详细字段' : '显示详细字段'}
                  <svg
                    className={`w-4 h-4 ml-1 transform transition-transform ${showDetailedFields[index] ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              {/* 详细字段 */}
              {showDetailedFields[index] && (
                <div className="mt-6 space-y-4 border-t pt-4">
                  {/* 学业成绩与发展 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CustomSelectField
                      label="学科强项分析"
                      fieldName="subjectStrengths"
                      value={student.subjectStrengths}
                      onChange={(value) => updateStudent(index, 'subjectStrengths', value)}
                      placeholder="请选择学科强项"
                    />

                    <CustomSelectField
                      label="学科薄弱点分析"
                      fieldName="subjectWeaknesses"
                      value={student.subjectWeaknesses}
                      onChange={(value) => updateStudent(index, 'subjectWeaknesses', value)}
                      placeholder="请选择学科薄弱点"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        学习潜力评估
                      </label>
                      <select
                        value={student.learningPotential}
                        onChange={(e) => updateStudent(index, 'learningPotential', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="潜力较大">潜力较大</option>
                        <option value="学习能力稳定">学习能力稳定</option>
                        <option value="需要进一步激发潜力">需要进一步激发潜力</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        对学科的兴趣程度
                      </label>
                      <select
                        value={student.subjectInterest}
                        onChange={(e) => updateStudent(index, 'subjectInterest', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="浓厚">浓厚</option>
                        <option value="一般">一般</option>
                        <option value="缺乏兴趣">缺乏兴趣</option>
                      </select>
                    </div>
                  </div>

                  {/* 课堂表现与参与 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      课堂专注度
                    </label>
                    <select
                      value={student.classroomConcentration}
                      onChange={(e) => updateStudent(index, 'classroomConcentration', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="始终高度专注">始终高度专注</option>
                      <option value="大部分时间专注">大部分时间专注</option>
                      <option value="有时分散注意力">有时分散注意力</option>
                      <option value="容易受外界干扰">容易受外界干扰</option>
                    </select>
                  </div>

                  {/* 行为习惯与态度 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        作业完成情况
                      </label>
                      <select
                        value={student.homeworkCompletion}
                        onChange={(e) => updateStudent(index, 'homeworkCompletion', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="总是按时高质量">总是按时高质量</option>
                        <option value="大部分按时完成">大部分按时完成</option>
                        <option value="有时拖延或应付">有时拖延或应付</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        学习主动性与自觉性
                      </label>
                      <select
                        value={student.learningProactiveness}
                        onChange={(e) => updateStudent(index, 'learningProactiveness', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="学习主动性强">学习主动性强</option>
                        <option value="完成要求任务">完成要求任务</option>
                        <option value="需要督促">需要督促</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        遵守纪律与规章
                      </label>
                      <select
                        value={student.disciplineCompliance}
                        onChange={(e) => updateStudent(index, 'disciplineCompliance', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="严格遵守">严格遵守</option>
                        <option value="基本遵守">基本遵守</option>
                        <option value="需要加强">需要加强</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        待人接物态度
                      </label>
                      <select
                        value={student.attitudeTowardsOthers}
                        onChange={(e) => updateStudent(index, 'attitudeTowardsOthers', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="有礼貌尊重他人">有礼貌尊重他人</option>
                        <option value="基本有礼貌">基本有礼貌</option>
                        <option value="不够尊重">不够尊重</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        责任心
                      </label>
                      <select
                        value={student.responsibility}
                        onChange={(e) => updateStudent(index, 'responsibility', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="较强">较强</option>
                        <option value="能完成分配任务">能完成分配任务</option>
                        <option value="有待提高">有待提高</option>
                      </select>
                    </div>
                  </div>

                  {/* 教师期望与个性化建议 */}
                  <CustomSelectField
                    label="教师总体评价"
                    fieldName="overallAssessment"
                    value={student.overallAssessment}
                    onChange={(value) => updateStudent(index, 'overallAssessment', value)}
                    placeholder="请选择总体评价"
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CustomSelectField
                      label="未来发展期望"
                      fieldName="futureExpectations"
                      value={student.futureExpectations}
                      onChange={(value) => updateStudent(index, 'futureExpectations', value)}
                      placeholder="请选择发展期望"
                    />

                    <CustomSelectField
                      label="针对性改进建议"
                      fieldName="improvementSuggestions"
                      value={student.improvementSuggestions}
                      onChange={(value) => updateStudent(index, 'improvementSuggestions', value)}
                      placeholder="请选择改进建议"
                    />
                  </div>
                </div>
              )}

              {!showDetailedFields[index] && (
                <div className="mt-4 text-sm text-gray-500">
                  其他详细信息将使用系统默认值，生成后可在历史记录中查看和编辑
                </div>
              )}
            </div>
          ))}
        </div>

        {students.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">还没有添加任何学生</div>
            <button
              onClick={addStudent}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              添加第一个学生
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
