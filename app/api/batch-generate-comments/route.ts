import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { BatchGenerationRequest, BatchGenerationResponse, StudentInfo } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '认证令牌无效或已过期'
      }, { status: 401 });
    }

    // 解析请求数据
    const requestBody = await request.json();
    console.log('收到的请求数据:', JSON.stringify(requestBody, null, 2));

    const { students, globalSettings }: BatchGenerationRequest = requestBody;

    // 验证输入数据
    if (!students || !Array.isArray(students) || students.length === 0) {
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '请提供有效的学生信息列表'
      }, { status: 400 });
    }

    if (students.length > 50) {
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '单次批量生成最多支持50个学生'
      }, { status: 400 });
    }

    // 验证每个学生的必填字段
    for (let i = 0; i < students.length; i++) {
      const student = students[i];
      if (!student.studentName || !student.studentName.trim()) {
        return NextResponse.json<BatchGenerationResponse>({
          success: false,
          message: `第${i + 1}个学生的姓名不能为空`
        }, { status: 400 });
      }
    }

    // 应用全局设置到每个学生
    const studentsWithGlobalSettings = students.map(student => ({
      ...student,
      commentTone: (globalSettings.commentTone || student.commentTone) as '温和亲切' | '严谨正式' | '鼓励激励' | '客观中性',
      wordCountRange: (globalSettings.wordCountRange || student.wordCountRange) as '150-200字' | '200-300字' | '300-400字' | '400-500字',
      commentPerspective: (globalSettings.commentPerspective || student.commentPerspective) as '你' | '该生'
    }));

    // 创建批量生成任务
    const taskData = {
      user_id: payload.userId,
      total_students: students.length,
      completed_count: 0,
      status: 'pending'
    };

    const { data: batchTask, error: taskError } = await supabaseAdmin
      .from('batch_generation_tasks')
      .insert(taskData)
      .select()
      .single();

    if (taskError || !batchTask) {
      console.error('创建批量任务失败:', taskError);
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '创建批量任务失败'
      }, { status: 500 });
    }

    // 创建批量生成结果记录
    const resultRecords = studentsWithGlobalSettings.map(student => ({
      batch_task_id: batchTask.id,
      student_name: student.studentName,
      student_info: student,
      status: 'pending' as const
    }));

    const { error: resultsError } = await supabaseAdmin
      .from('batch_generation_results')
      .insert(resultRecords);

    if (resultsError) {
      console.error('创建批量结果记录失败:', resultsError);
      return NextResponse.json<BatchGenerationResponse>({
        success: false,
        message: '创建批量结果记录失败'
      }, { status: 500 });
    }

    // 启动后台批量生成任务
    // 注意：这里我们不等待完成，而是立即返回任务ID
    processBatchGeneration(batchTask.id, payload.userId, studentsWithGlobalSettings);

    return NextResponse.json<BatchGenerationResponse>({
      success: true,
      message: '批量生成任务已启动',
      batchTaskId: batchTask.id
    });

  } catch (error) {
    console.error('批量生成评语错误:', error);
    return NextResponse.json<BatchGenerationResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 后台处理批量生成
async function processBatchGeneration(batchTaskId: string, userId: string, students: StudentInfo[]) {
  try {
    // 更新任务状态为处理中
    await supabaseAdmin
      .from('batch_generation_tasks')
      .update({ status: 'processing' })
      .eq('id', batchTaskId);

    let completedCount = 0;
    let totalTokensUsed = 0;

    // 逐个生成评语
    for (const student of students) {
      try {
        // 获取对应的结果记录
        const { data: resultRecord } = await supabaseAdmin
          .from('batch_generation_results')
          .select('id')
          .eq('batch_task_id', batchTaskId)
          .eq('student_name', student.studentName)
          .single();

        if (!resultRecord) {
          console.error(`找不到学生 ${student.studentName} 的结果记录`);
          continue;
        }

        // 更新结果状态为处理中
        await supabaseAdmin
          .from('batch_generation_results')
          .update({ status: 'processing' })
          .eq('id', resultRecord.id);

        // 调用AI生成评语
        const result = await generateSingleComment(student);

        if (result.success && result.comment) {
          // 更新成功结果
          await supabaseAdmin
            .from('batch_generation_results')
            .update({
              generated_comment: result.comment,
              tokens_used: result.tokensUsed || 0,
              status: 'success'
            })
            .eq('id', resultRecord.id);

          // 保存到评语历史记录
          await supabaseAdmin
            .from('comments')
            .insert({
              user_id: userId,
              student_name: student.studentName,
              student_info: student,
              generated_comment: result.comment,
              tokens_used: result.tokensUsed || 0
            });

          totalTokensUsed += result.tokensUsed || 0;
          completedCount++;
        } else {
          // 更新失败结果
          await supabaseAdmin
            .from('batch_generation_results')
            .update({
              status: 'failed',
              error_message: result.error || '生成失败'
            })
            .eq('id', resultRecord.id);
        }

        // 更新任务进度
        await supabaseAdmin
          .from('batch_generation_tasks')
          .update({ completed_count: completedCount })
          .eq('id', batchTaskId);

        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`生成学生 ${student.studentName} 评语失败:`, error);
        
        // 更新失败结果
        const { data: resultRecord } = await supabaseAdmin
          .from('batch_generation_results')
          .select('id')
          .eq('batch_task_id', batchTaskId)
          .eq('student_name', student.studentName)
          .single();

        if (resultRecord) {
          await supabaseAdmin
            .from('batch_generation_results')
            .update({
              status: 'failed',
              error_message: error instanceof Error ? error.message : '未知错误'
            })
            .eq('id', resultRecord.id);
        }
      }
    }

    // 记录总Token使用情况
    if (totalTokensUsed > 0) {
      await supabaseAdmin
        .from('token_usage')
        .insert({
          user_id: userId,
          tokens_used: totalTokensUsed,
          api_call_type: 'batch_comment_generation',
          created_at: new Date().toISOString()
        });
    }

    // 更新任务状态为完成
    await supabaseAdmin
      .from('batch_generation_tasks')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', batchTaskId);

  } catch (error) {
    console.error('批量生成处理失败:', error);
    
    // 更新任务状态为失败
    await supabaseAdmin
      .from('batch_generation_tasks')
      .update({ status: 'failed' })
      .eq('id', batchTaskId);
  }
}

// 生成单个评语的函数
async function generateSingleComment(studentInfo: StudentInfo): Promise<{
  success: boolean;
  comment?: string;
  tokensUsed?: number;
  error?: string;
}> {
  try {
    const prompt = buildPrompt(studentInfo);

    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 1000,
        stream: false
      })
    });

    if (!response.ok) {
      return { success: false, error: 'AI服务暂时不可用' };
    }

    const data = await response.json();
    const comment = data.choices?.[0]?.message?.content?.trim();
    const tokensUsed = data.usage?.total_tokens || 0;

    if (!comment) {
      return { success: false, error: '生成的评语为空' };
    }

    return { success: true, comment, tokensUsed };

  } catch (error) {
    console.error('生成单个评语错误:', error);
    return { success: false, error: '生成评语时发生错误' };
  }
}

function buildPrompt(studentInfo: StudentInfo): string {
  const toneGuidance = getToneGuidance(studentInfo.commentTone);

  return `请扮演一位经验丰富、富有同情心、真正关心每一个学生的班主任。你的任务是根据我将提供给你的每位学生的详细结构化数据，为学生生成一份针对当前学期或学年的个性化、全面且具有发展性的评语。

学生信息：
- 学生姓名：${studentInfo.studentName}
- 学生性别：${studentInfo.studentGender}
- 学科强项分析：${studentInfo.subjectStrengths}
- 学科薄弱点分析：${studentInfo.subjectWeaknesses}
- 学习潜力评估：${studentInfo.learningPotential}
- 对学科的兴趣程度：${studentInfo.subjectInterest}
- 课堂专注度：${studentInfo.classroomConcentration}
- 作业完成情况：${studentInfo.homeworkCompletion}
- 学习主动性与自觉性：${studentInfo.learningProactiveness}
- 遵守纪律与规章：${studentInfo.disciplineCompliance}
- 待人接物态度：${studentInfo.attitudeTowardsOthers}
- 责任心：${studentInfo.responsibility}
- 特长与兴趣：${studentInfo.talentsAndInterests}
- 班干职位：${studentInfo.classPosition}
- 获奖情况：${studentInfo.awards}
- 教师总体评价：${studentInfo.overallAssessment}
- 未来发展期望：${studentInfo.futureExpectations}
- 针对性改进建议/鼓励方向：${studentInfo.improvementSuggestions}

评语要求：
1. 根据评语人称"${studentInfo.commentPerspective}"来称呼学生
2. 评语长度控制在${studentInfo.wordCountRange}字
3. ${toneGuidance}
4. 积极正面为主，委婉地指出需要改进的方面
5. 针对性强，避免泛泛之谈
6. 体现发展性视角，既肯定优点也指出改进方向

请直接输出生成的学生评语文本内容，不需要包含任何额外的标题、说明或格式标记。`;
}

function getToneGuidance(tone: string): string {
  switch (tone) {
    case '温和亲切':
      return '语言温和亲切、充满关爱，如慈祥的长辈对晚辈的关怀，用词温暖，语调柔和，体现出对学生的深切关心和理解';
    case '严谨正式':
      return '语言严谨正式、条理清晰，如专业教育工作者的评价，用词准确，逻辑严密，体现出专业性和权威性';
    case '鼓励激励':
      return '语言充满鼓励和激励，积极向上、富有感染力，如教练对运动员的激励，用词振奋人心，能够激发学生的斗志和潜能';
    case '客观中性':
      return '语言客观中性、实事求是，如研究者的客观分析，用词准确客观，避免过于情感化的表达，注重事实描述';
    default:
      return '语言亲切自然、真诚恳切、具体形象、生动有趣';
  }
}
